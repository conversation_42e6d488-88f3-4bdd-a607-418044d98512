# Comave Email Config Module

## Overview

This module provides email URL redirection functionality to ensure that all URLs in email templates point to the correct frontend domain instead of the backend domain.

## Problem Solved

When Magento sends emails (customer registration, password reset, order confirmation, etc.), the URLs in these emails often point to the backend domain (e.g., `mcstaging.comave.com`) instead of the frontend domain (e.g., `staging.comave.com`). This module automatically rewrites these URLs to point to the correct frontend domain.

## Supported Email Types

- Customer registration
- Password reset
- Order confirmation
- Order comment updates
- Order shipment updates
- Refunds
- Credit memos
- All other email templates

## Environment Support

The module automatically detects the environment and maps backend domains to frontend domains:

- **Production**: `mc.comave.com` → `comave.com`
- **Staging**: `mcstaging.comave.com` → `staging.comave.com`
- **Development**: `mcdev.comave.com` → `dev.comave.com`
- **Local**: `localhost:8080` → `localhost:3000`

## How It Works

The module uses Magento plugins to intercept email template processing:

1. **EmailTemplateUrlPlugin**: Intercepts `Magento\Email\Model\Template\Filter` methods to rewrite URLs in email content
2. **EmailTemplatePlugin**: Intercepts `Magento\Email\Model\Template` methods to ensure final email output has correct URLs
3. **EmailUrlRewriter**: Service class that handles the actual URL rewriting logic

## Configuration

The module uses system configuration values for frontend URLs:

- `web/unsecure/frontend_base_url` - Frontend unsecure base URL
- `web/secure/frontend_base_url` - Frontend secure base URL

These can be configured in:
**Admin Panel** → **Stores** → **Configuration** → **Web** → **Unsecure/Secure**

## Technical Details

### URL Rewriting Process

1. Detects current backend domain from Magento configuration
2. Maps backend domain to corresponding frontend domain
3. Replaces URLs in:
   - `href` attributes in links
   - `src` attributes in images
   - Plain text URLs

### Plugin Architecture

- Uses `afterFilter`, `afterBlockDirective`, `afterVarDirective` methods to catch all URL generation
- Uses `afterProcessTemplate` and `afterGetProcessedTemplate` to ensure final output is correct
- Only affects email content, not global URL generation

## Logging

The module logs URL rewriting activities for debugging:

- Debug logs when no frontend domain mapping is found
- Debug logs when URLs are successfully rewritten
- Logs can be found in Magento's debug log files

## Files Structure

```
app/code/Comave/EmailConfig/
├── Plugin/
│   ├── EmailTemplateUrlPlugin.php    # Main URL rewriting plugin
│   └── EmailTemplatePlugin.php       # Template processing plugin
├── Service/
│   └── EmailUrlRewriter.php          # URL rewriting logic
├── Model/
│   ├── Config/
│   │   └── Config.php                # Configuration helper
│   └── Url.php                       # Custom URL class
├── etc/
│   ├── di.xml                        # Dependency injection configuration
│   ├── module.xml                    # Module definition
│   └── adminhtml/
│       └── system.xml                # Admin configuration
└── README.md                         # This file
```

## Installation

The module is already installed and configured. No additional setup is required.

## Testing

To test the module:

1. Send a test email (e.g., password reset)
2. Check that URLs in the email point to the frontend domain
3. Check debug logs for URL rewriting activity

## Troubleshooting

If URLs are not being rewritten:

1. Check that the backend domain is correctly detected
2. Verify frontend domain configuration in admin panel
3. Check debug logs for error messages
4. Ensure module is enabled and plugins are working
