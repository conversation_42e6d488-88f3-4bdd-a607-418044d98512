<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Model\Config;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Psr\Log\LoggerInterface;

class Config
{
    private const string XML_PATH_FRONTEND_UNSECURED_URL = 'web/unsecure/frontend_base_url';
    private const string XML_PATH_FRONTEND_SECURED_URL = 'web/secure/frontend_base_url';

    private const string XML_PATH_BACKEND_UNSECURED_URL = 'web/unsecure/base_url';
    private const string XML_PATH_BACKEND_SECURED_URL = 'web/secure/base_url';

    private const ENVIRONMENT_DOMAINS = [
        'production' => [
            'backend' => 'mc.comave.com',
            'frontend' => 'comave.com'
        ],
        'staging' => [
            'backend' => 'mcstaging.comave.com',
            'frontend' => 'staging.comave.com'
        ],
        'development' => [
            'backend' => 'mcdev.comave.com',
            'frontend' => 'dev.comave.com'
        ],
        'local' => [
            'backend' => 'localhost:8080',
            'frontend' => 'localhost:3000'
        ]
    ];

    public function __construct(
        private readonly ScopeConfigInterface $storeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    public function getFrontendSecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_SECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    public function getFrontendUnsecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_UNSECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    public function getBackendSecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_BACKEND_SECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    public function getBackendUnsecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_BACKEND_UNSECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    /**
     * Detect current environment based on backend domain
     *
     * @return string|null
     */
    public function getCurrentEnvironment(): ?string
    {
        $this->logger->info('EmailConfig: STEP 1 - Starting environment detection');

        $unsecureUrl = $this->storeConfig->getValue('web/unsecure/base_url');
        $secureUrl = $this->storeConfig->getValue('web/secure/base_url');
        $baseUrl = $unsecureUrl ?: $secureUrl;

        $this->logger->info('EmailConfig: STEP 2 - Unsecure URL: ' . ($unsecureUrl ?? 'null'));
        $this->logger->info('EmailConfig: STEP 3 - Secure URL: ' . ($secureUrl ?? 'null'));
        $this->logger->info('EmailConfig: STEP 4 - Selected Base URL: ' . ($baseUrl ?? 'null'));

        if (!$baseUrl) {
            $this->logger->warning('EmailConfig: STEP 5 - ERROR: No base URL found in configuration');
            return null;
        }

        $parsedUrl = parse_url($baseUrl);
        $currentDomain = $parsedUrl['host'] ?? '';

        $this->logger->info('EmailConfig: STEP 6 - Parsed host: ' . $currentDomain);

        if (isset($parsedUrl['port']) && !in_array($parsedUrl['port'], [80, 443])) {
            $currentDomain .= ':' . $parsedUrl['port'];
            $this->logger->info('EmailConfig: STEP 7 - Added port to domain: ' . $currentDomain);
        }

        $this->logger->info('EmailConfig: STEP 8 - Final current domain: ' . $currentDomain);
        $this->logger->info('EmailConfig: STEP 9 - Available environment domains: ' . json_encode(self::ENVIRONMENT_DOMAINS));

        foreach (self::ENVIRONMENT_DOMAINS as $env => $domains) {
            $this->logger->info('EmailConfig: STEP 10 - Checking environment ' . $env . ' with backend domain: ' . $domains['backend']);
            if ($domains['backend'] === $currentDomain) {
                $this->logger->info('EmailConfig: STEP 11 - SUCCESS: Environment detected: ' . $env);
                return $env;
            }
        }

        $this->logger->warning('EmailConfig: STEP 12 - ERROR: No environment match found for domain: ' . $currentDomain);
        return null;
    }

    /**
     * Get frontend domain for current environment
     *
     * @return string|null
     */
    public function getCurrentFrontendDomain(): ?string
    {
        $this->logger->info('EmailConfig: STEP 13 - Getting frontend domain');

        $environment = $this->getCurrentEnvironment();

        $this->logger->info('EmailConfig: STEP 14 - Environment result: ' . ($environment ?? 'null'));

        if (!$environment || !isset(self::ENVIRONMENT_DOMAINS[$environment])) {
            $this->logger->warning('EmailConfig: STEP 15 - ERROR: Invalid environment or missing domain mapping');
            return null;
        }

        $frontendDomain = self::ENVIRONMENT_DOMAINS[$environment]['frontend'];
        $this->logger->info('EmailConfig: STEP 16 - SUCCESS: Frontend domain: ' . $frontendDomain);

        return $frontendDomain;
    }

    /**
     * Get backend domain for current environment
     *
     * @return string|null
     */
    public function getCurrentBackendDomain(): ?string
    {
        $this->logger->info('EmailConfig: STEP 17 - Getting backend domain');

        $environment = $this->getCurrentEnvironment();

        $this->logger->info('EmailConfig: STEP 18 - Environment result: ' . ($environment ?? 'null'));

        if (!$environment || !isset(self::ENVIRONMENT_DOMAINS[$environment])) {
            $this->logger->warning('EmailConfig: STEP 19 - ERROR: Invalid environment or missing domain mapping');
            return null;
        }

        $backendDomain = self::ENVIRONMENT_DOMAINS[$environment]['backend'];
        $this->logger->info('EmailConfig: STEP 20 - SUCCESS: Backend domain: ' . $backendDomain);

        return $backendDomain;
    }
}
