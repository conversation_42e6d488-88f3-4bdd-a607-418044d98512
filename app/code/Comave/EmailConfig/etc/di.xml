<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Plugin to intercept email template URL generation ONLY (not all URL generation) -->
    <type name="Magento\Email\Model\Template\Filter">
        <plugin name="email_template_url_plugin" type="Comave\EmailConfig\Plugin\EmailTemplateUrlPlugin" sortOrder="10"/>
    </type>

    <!-- Plugin to intercept URL generation in email template blocks -->
    <type name="Magento\Email\Model\Template">
        <plugin name="email_template_url_rewriter_plugin" type="Comave\EmailConfig\Plugin\EmailTemplatePlugin" sortOrder="10"/>
    </type>
</config>
