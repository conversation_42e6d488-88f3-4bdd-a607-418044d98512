<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Email\Model\Template;

/**
 * Plugin to intercept email template processing and rewrite URLs
 * 
 * This plugin targets the email template processing to ensure all URLs
 * generated during template processing are rewritten from backend to frontend domains.
 */
class EmailTemplatePlugin
{
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter
    ) {
    }

    /**
     * Intercept email template processing to rewrite URLs in final output
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterProcessTemplate(Template $subject, string $result): string
    {
        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }

    /**
     * Intercept email template text processing to rewrite URLs
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterGetProcessedTemplate(Template $subject, string $result): string
    {
        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }
}
