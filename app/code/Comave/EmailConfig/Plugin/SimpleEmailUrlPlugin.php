<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Email\Model\Template;
use Psr\Log\LoggerInterface;

/**
 * Simple plugin to rewrite URLs in email content from backend to frontend domain
 */
class SimpleEmailUrlPlugin
{
    public function __construct(
        private readonly Config $config,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Rewrite URLs in final email content
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterProcessTemplate(Template $subject, string $result): string
    {
        $this->logger->info('SimpleEmailUrlPlugin: Processing email template');

        $rewrittenResult = $this->rewriteUrls($result);

        if ($rewrittenResult !== $result) {
            $this->logger->info('SimpleEmailUrlPlugin: URLs were rewritten in email content');
        }

        return $rewrittenResult;
    }

    /**
     * Rewrite URLs in email content
     *
     * @param string $content
     * @return string
     */
    private function rewriteUrls(string $content): string
    {
        // Get current backend and frontend domains from config
        $backendDomain = $this->config->getBackendSecureUrl();
        $frontendDomain = $this->config->getFrontendSecureUrl();

        $this->logger->info('SimpleEmailUrlPlugin: Backend domain: ' . ($backendDomain ?? 'null'));
        $this->logger->info('SimpleEmailUrlPlugin: Frontend domain: ' . ($frontendDomain ?? 'null'));
        
        $this->logger->info('Email ' . $content);

        if (!$backendDomain || !$frontendDomain) {
            $this->logger->warning('SimpleEmailUrlPlugin: Could not determine backend or frontend domain');
            return $content;
        }

        $content = preg_replace_callback(
            '/href=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']/',
            function ($matches) use ($backendDomain, $frontendDomain) {
                $url = str_replace($backendDomain, $frontendDomain, $matches[1]);
                $this->logger->info('SimpleEmailUrlPlugin: Rewritten href URL: ' . $matches[1] . ' -> ' . $url);
                return 'href="' . $url . '"';
            },
            $content
        );

        $httpsReplaced = str_replace(
            'https://' . $backendDomain,
            'https://' . $frontendDomain,
            $content
        );

        $httpReplaced = str_replace(
            'http://' . $backendDomain,
            'http://' . $frontendDomain,
            $httpsReplaced
        );

        return $httpReplaced;
    }
}
