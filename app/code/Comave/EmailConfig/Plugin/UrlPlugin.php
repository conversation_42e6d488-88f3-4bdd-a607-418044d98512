<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Url;
use Psr\Log\LoggerInterface;

/**
 * Plugin to intercept URL generation and rewrite URLs when in email context
 * 
 * This plugin intercepts the core URL builder to detect when URLs are being generated
 * in email context and applies domain rewriting from backend to frontend domains.
 */
class UrlPlugin
{
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter,
        private readonly State $appState,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Intercept URL generation to rewrite backend URLs to frontend URLs in email context
     *
     * @param Url $subject
     * @param string $result
     * @param string|null $routePath
     * @param array|null $routeParams
     * @return string
     */
    public function afterGetUrl(Url $subject, string $result, ?string $routePath = null, ?array $routeParams = null): string
    {
        // For debugging: temporarily apply to all URLs to see if it works
        // TODO: Remove this and uncomment the email context check once working
        $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);

        if ($rewrittenUrl !== $result) {
            $this->logger->debug('UrlPlugin: Rewritten URL from ' . $result . ' to ' . $rewrittenUrl . ' (Route: ' . ($routePath ?? 'null') . ')');
        }

        return $rewrittenUrl;

        // Only apply URL rewriting if we're in email context
        // if (!$this->isEmailContext()) {
        //     return $result;
        // }

        // $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);

        // if ($rewrittenUrl !== $result) {
        //     $this->logger->debug('UrlPlugin: Rewritten URL from ' . $result . ' to ' . $rewrittenUrl);
        // }

        // return $rewrittenUrl;
    }

    /**
     * Intercept base URL generation to rewrite backend URLs to frontend URLs in email context
     *
     * @param Url $subject
     * @param string $result
     * @param array $params
     * @return string
     */
    public function afterGetBaseUrl(Url $subject, string $result, array $params = []): string
    {
        // For debugging: temporarily apply to all URLs to see if it works
        $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);

        if ($rewrittenUrl !== $result) {
            $this->logger->debug('UrlPlugin: Rewritten base URL from ' . $result . ' to ' . $rewrittenUrl);
        }

        return $rewrittenUrl;
    }

    /**
     * Intercept route URL generation to rewrite backend URLs to frontend URLs in email context
     *
     * @param Url $subject
     * @param string $result
     * @param string|null $routePath
     * @param array|null $routeParams
     * @return string
     */
    public function afterGetRouteUrl(Url $subject, string $result, ?string $routePath = null, ?array $routeParams = null): string
    {
        // Only apply URL rewriting if we're in email context
        if (!$this->isEmailContext()) {
            return $result;
        }

        $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);
        
        if ($rewrittenUrl !== $result) {
            $this->logger->debug('UrlPlugin: Rewritten route URL from ' . $result . ' to ' . $rewrittenUrl);
        }

        return $rewrittenUrl;
    }

    /**
     * Determine if we're currently in email context
     *
     * @return bool
     */
    private function isEmailContext(): bool
    {
        try {
            // Check call stack for email-related classes
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 30);

            foreach ($backtrace as $trace) {
                if (isset($trace['class'])) {
                    $class = $trace['class'];

                    // Check for email-related classes in the call stack
                    if (strpos($class, 'Email') !== false ||
                        strpos($class, 'Template') !== false ||
                        strpos($class, 'Mail') !== false ||
                        strpos($class, 'Transport') !== false ||
                        strpos($class, 'Filter') !== false ||
                        strpos($class, 'Sender') !== false) {
                        return true;
                    }
                }

                if (isset($trace['function'])) {
                    $function = $trace['function'];

                    // Check for email-related function names
                    if (strpos($function, 'email') !== false ||
                        strpos($function, 'template') !== false ||
                        strpos($function, 'mail') !== false ||
                        strpos($function, 'send') !== false) {
                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            // If we can't determine the context, don't apply rewriting to be safe
            return false;
        }
    }
}
