<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Email\Model\Template\Filter;

/**
 * Plugin to intercept email template URL generation and redirect backend URLs to frontend
 * 
 * This plugin specifically targets email content processing to replace backend domain URLs
 * (mcstaging.comave.com) with frontend domain URLs (staging.comave.com) for all email types:
 * - Customer registration
 * - Password reset
 * - Order confirmation
 * - Order comment updates
 * - Shipment updates
 * - Refunds
 * - Credit memos
 */
class EmailTemplateUrlPlugin
{
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter
    ) {
    }

    /**
     * Intercept email template filter processing to rewrite URLs
     *
     * This method intercepts the filter() method which processes email template content
     * and applies URL rewriting specifically for email context only.
     *
     * @param Filter $subject
     * @param string $result
     * @return string
     */
    public function afterFilter(Filter $subject, string $result): string
    {
        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }

    /**
     * Intercept block directive processing to rewrite URLs in block output
     *
     * @param Filter $subject
     * @param string $result
     * @return string
     */
    public function afterBlockDirective(Filter $subject, string $result): string
    {
        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }

    /**
     * Intercept variable directive processing to rewrite URLs in variable output
     *
     * @param Filter $subject
     * @param string $result
     * @return string
     */
    public function afterVarDirective(Filter $subject, string $result): string
    {
        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }
}
