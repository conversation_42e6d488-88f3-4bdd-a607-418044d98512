<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;

/**
 * Plugin to intercept Store URL generation and rewrite URLs when in email context
 * 
 * This plugin intercepts the Store model URL generation methods to detect when URLs 
 * are being generated in email context and applies domain rewriting.
 */
class StoreUrlPlugin
{
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Intercept store URL generation to rewrite backend URLs to frontend URLs in email context
     *
     * @param Store $subject
     * @param string $result
     * @param string $route
     * @param array $params
     * @return string
     */
    public function afterGetUrl(Store $subject, string $result, string $route = '', array $params = []): string
    {
        // Only apply URL rewriting if we're in email context
        if (!$this->isEmailContext()) {
            return $result;
        }

        $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);
        
        if ($rewrittenUrl !== $result) {
            $this->logger->debug('StoreUrlPlugin: Rewritten store URL from ' . $result . ' to ' . $rewrittenUrl);
        }

        return $rewrittenUrl;
    }

    /**
     * Intercept base URL generation to rewrite backend URLs to frontend URLs in email context
     *
     * @param Store $subject
     * @param string $result
     * @param string $type
     * @param bool|null $secure
     * @return string
     */
    public function afterGetBaseUrl(Store $subject, string $result, string $type = 'link', ?bool $secure = null): string
    {
        // Only apply URL rewriting if we're in email context
        if (!$this->isEmailContext()) {
            return $result;
        }

        $rewrittenUrl = $this->emailUrlRewriter->rewriteEmailUrls($result);
        
        if ($rewrittenUrl !== $result) {
            $this->logger->debug('StoreUrlPlugin: Rewritten store base URL from ' . $result . ' to ' . $rewrittenUrl);
        }

        return $rewrittenUrl;
    }

    /**
     * Determine if we're currently in email context
     *
     * @return bool
     */
    private function isEmailContext(): bool
    {
        try {
            // Check call stack for email-related classes
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 30);
            
            foreach ($backtrace as $trace) {
                if (isset($trace['class'])) {
                    $class = $trace['class'];
                    
                    // Check for email-related classes in the call stack
                    if (strpos($class, 'Email') !== false ||
                        strpos($class, 'Template') !== false ||
                        strpos($class, 'Mail') !== false ||
                        strpos($class, 'Transport') !== false ||
                        strpos($class, 'Filter') !== false ||
                        strpos($class, 'Sender') !== false) {
                        return true;
                    }
                }
                
                if (isset($trace['function'])) {
                    $function = $trace['function'];
                    
                    // Check for email-related function names
                    if (strpos($function, 'email') !== false ||
                        strpos($function, 'template') !== false ||
                        strpos($function, 'mail') !== false ||
                        strpos($function, 'send') !== false) {
                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            // If we can't determine the context, don't apply rewriting to be safe
            return false;
        }
    }
}
