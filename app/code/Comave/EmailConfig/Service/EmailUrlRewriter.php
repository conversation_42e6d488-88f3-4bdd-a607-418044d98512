<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Service;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

/**
 * Service class to handle URL rewriting in email content
 * 
 * Handles the logic for detecting and replacing backend domain URLs with frontend domain URLs
 * in email content. Supports multiple environments (production, staging, development).
 */
class EmailUrlRewriter
{
    private const BACKEND_DOMAINS = [
        'mcstaging.comave.com',     // Staging backend
        'mc.comave.com',            // Production backend  
        'mcdev.comave.com',         // Development backend
        'localhost:8080',           // Local development backend
    ];

    private const FRONTEND_DOMAIN_MAPPING = [
        'mcstaging.comave.com' => 'staging.comave.com',
        'mc.comave.com' => 'comave.com',
        'mcdev.comave.com' => 'dev.comave.com',
        'localhost:8080' => 'localhost:3000',
    ];

    public function __construct(
        private readonly Config $config,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Rewrite URLs in email content from backend domain to frontend domain
     *
     * @param string $emailContent
     * @return string
     */
    public function rewriteEmailUrls(string $emailContent): string
    {
        if (empty($emailContent)) {
            return $emailContent;
        }

        $currentBackendDomain = $this->getCurrentBackendDomain();
        $frontendDomain = $this->getFrontendDomainForBackend($currentBackendDomain);

        if (!$frontendDomain) {
            $this->logger->debug('EmailUrlRewriter: No frontend domain mapping found for backend domain: ' . ($currentBackendDomain ?? 'null'));
            return $emailContent;
        }

        $this->logger->debug('EmailUrlRewriter: Rewriting URLs from ' . $currentBackendDomain . ' to ' . $frontendDomain);

        $originalContent = $emailContent;

        // Replace URLs in href attributes
        $emailContent = $this->replaceHrefUrls($emailContent, $currentBackendDomain, $frontendDomain);

        // Replace URLs in src attributes (for images)
        $emailContent = $this->replaceSrcUrls($emailContent, $currentBackendDomain, $frontendDomain);

        // Replace plain text URLs
        $emailContent = $this->replacePlainTextUrls($emailContent, $currentBackendDomain, $frontendDomain);

        if ($originalContent !== $emailContent) {
            $this->logger->debug('EmailUrlRewriter: URLs were rewritten in email content');
        }

        return $emailContent;
    }

    /**
     * Get the current backend domain from request or configuration
     *
     * @return string|null
     */
    private function getCurrentBackendDomain(): ?string
    {
        // Get current base URL from configuration
        $baseUrl = $this->scopeConfig->getValue(
            'web/unsecure/base_url',
            ScopeInterface::SCOPE_STORE
        );

        if (!$baseUrl) {
            $baseUrl = $this->scopeConfig->getValue(
                'web/secure/base_url',
                ScopeInterface::SCOPE_STORE
            );
        }

        if ($baseUrl) {
            $parsedUrl = parse_url($baseUrl);
            $domain = $parsedUrl['host'] ?? '';
            
            if (isset($parsedUrl['port']) && !in_array($parsedUrl['port'], [80, 443])) {
                $domain .= ':' . $parsedUrl['port'];
            }

            // Check if this is one of our known backend domains
            if (in_array($domain, self::BACKEND_DOMAINS)) {
                return $domain;
            }
        }

        return null;
    }

    /**
     * Get frontend domain for given backend domain
     *
     * @param string|null $backendDomain
     * @return string|null
     */
    private function getFrontendDomainForBackend(?string $backendDomain): ?string
    {
        if (!$backendDomain || !isset(self::FRONTEND_DOMAIN_MAPPING[$backendDomain])) {
            return null;
        }

        return self::FRONTEND_DOMAIN_MAPPING[$backendDomain];
    }

    /**
     * Replace URLs in href attributes
     *
     * @param string $content
     * @param string $backendDomain
     * @param string $frontendDomain
     * @return string
     */
    private function replaceHrefUrls(string $content, string $backendDomain, string $frontendDomain): string
    {
        $pattern = '/href=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']([^>]*>)/i';
        
        return preg_replace_callback($pattern, function ($matches) use ($backendDomain, $frontendDomain) {
            $originalUrl = $matches[1];
            $newUrl = str_replace($backendDomain, $frontendDomain, $originalUrl);
            return 'href="' . $newUrl . '"' . $matches[2];
        }, $content);
    }

    /**
     * Replace URLs in src attributes
     *
     * @param string $content
     * @param string $backendDomain
     * @param string $frontendDomain
     * @return string
     */
    private function replaceSrcUrls(string $content, string $backendDomain, string $frontendDomain): string
    {
        $pattern = '/src=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']([^>]*>)/i';
        
        return preg_replace_callback($pattern, function ($matches) use ($backendDomain, $frontendDomain) {
            $originalUrl = $matches[1];
            $newUrl = str_replace($backendDomain, $frontendDomain, $originalUrl);
            return 'src="' . $newUrl . '"' . $matches[2];
        }, $content);
    }

    /**
     * Replace plain text URLs (not in attributes)
     *
     * @param string $content
     * @param string $backendDomain
     * @param string $frontendDomain
     * @return string
     */
    private function replacePlainTextUrls(string $content, string $backendDomain, string $frontendDomain): string
    {
        $pattern = '/https?:\/\/' . preg_quote($backendDomain, '/') . '[^\s<>"\']*/i';
        
        return preg_replace_callback($pattern, function ($matches) use ($backendDomain, $frontendDomain) {
            return str_replace($backendDomain, $frontendDomain, $matches[0]);
        }, $content);
    }
}
